pip install torch torchvision Pillow opencv-python

import torch
from torchvision.transforms import ToTensor
from PIL import Image
import cv2

# Load model function
def load_model(model_path, model_type='srcnn'):
    model = torch.load(model_path, map_location=torch.device('cpu'))
    model.eval()  # Set to inference mode
    return model

# Process single image
def super_resolve(model, image_path, output_path, scale_factor=4):
    img = Image.open(image_path).convert('RGB')
    lr_img = img.resize((img.width//scale_factor, img.height//scale_factor), Image.BICUBIC)
    
    input_tensor = ToTensor()(lr_img).unsqueeze(0)
    with torch.no_grad():
        output = model(input_tensor)
    
    sr_img = output.squeeze(0).permute(1,2,0).numpy()
    cv2.imwrite(output_path, sr_img[:,:,::-1]*255)  # Save as PNG

# Example usage (choose one model)
model = load_model('srcnn_s2naip.pth', model_type='srcnn')  # Least resource-intensive
super_resolve(model, 'sentinel_sample.jp2', 'sr_output.png', scale_factor=4)

